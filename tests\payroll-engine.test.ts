// Comprehensive Payroll Engine Tests
// Phase 5: Testing and Documentation - Core Engine Testing

import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { PayrollCalculationEngine } from '@/lib/payroll-calculation-engine'
import { PayrollComponentsManager } from '@/lib/payroll-components'
import { NepaliCalendar } from '@/lib/nepali-calendar'
import { nepalConfig } from '@/lib/nepal-config'

describe('PayrollCalculationEngine', () => {
  let engine: PayrollCalculationEngine
  let componentsManager: PayrollComponentsManager

  beforeEach(() => {
    engine = new PayrollCalculationEngine()
    componentsManager = new PayrollComponentsManager()
  })

  describe('Monthly Salary Calculation', () => {
    it('should calculate basic monthly salary correctly', async () => {
      const employee = {
        id: 'EMP001',
        payStructure: 'monthly' as const,
        baseSalary: 65000,
        allowances: [],
        deductions: []
      }

      const attendanceData = {
        workingDays: 26,
        attendedDays: 26,
        overtimeHours: 0,
        lateHours: 0,
        earlyLeaveHours: 0
      }

      const result = await engine.calculateMonthlyPayroll(employee, attendanceData, '2024-11-01', '2024-11-30')

      expect(result.basePay).toBe(65000)
      expect(result.grossPay).toBe(65000)
      expect(result.netPay).toBeLessThan(65000) // After deductions
      expect(result.payStructure).toBe('monthly')
    })

    it('should handle partial month attendance correctly', async () => {
      const employee = {
        id: 'EMP001',
        payStructure: 'monthly' as const,
        baseSalary: 65000,
        allowances: [],
        deductions: []
      }

      const attendanceData = {
        workingDays: 26,
        attendedDays: 20, // 6 days absent
        overtimeHours: 0,
        lateHours: 0,
        earlyLeaveHours: 0
      }

      const result = await engine.calculateMonthlyPayroll(employee, attendanceData, '2024-11-01', '2024-11-30')

      const expectedBasePay = (65000 / 26) * 20 // Pro-rated for attendance
      expect(result.basePay).toBeCloseTo(expectedBasePay, 2)
      expect(result.attendanceDeduction).toBeGreaterThan(0)
    })

    it('should calculate overtime correctly for monthly employees', async () => {
      const employee = {
        id: 'EMP001',
        payStructure: 'monthly' as const,
        baseSalary: 65000,
        allowances: [],
        deductions: []
      }

      const attendanceData = {
        workingDays: 26,
        attendedDays: 26,
        overtimeHours: 10,
        lateHours: 0,
        earlyLeaveHours: 0
      }

      const result = await engine.calculateMonthlyPayroll(employee, attendanceData, '2024-11-01', '2024-11-30')

      expect(result.overtimePay).toBeGreaterThan(0)
      expect(result.grossPay).toBeGreaterThan(65000)
      
      // Overtime rate should be 1.5x for first 4 hours, 2x for next 4 hours, 2.5x for remaining
      const hourlyRate = 65000 / (26 * 8) // Monthly salary / (working days * 8 hours)
      const expectedOvertimePay = (hourlyRate * 1.5 * 4) + (hourlyRate * 2 * 4) + (hourlyRate * 2.5 * 2)
      expect(result.overtimePay).toBeCloseTo(expectedOvertimePay, 2)
    })
  })

  describe('Hourly Wage Calculation', () => {
    it('should calculate hourly wages correctly', async () => {
      const employee = {
        id: 'EMP002',
        payStructure: 'hourly' as const,
        hourlyRate: 500,
        allowances: [],
        deductions: []
      }

      const attendanceData = {
        workingDays: 26,
        attendedDays: 26,
        totalHours: 208, // 26 days * 8 hours
        overtimeHours: 0,
        lateHours: 0,
        earlyLeaveHours: 0
      }

      const result = await engine.calculateHourlyPayroll(employee, attendanceData, '2024-11-01', '2024-11-30')

      expect(result.basePay).toBe(208 * 500) // 104,000
      expect(result.grossPay).toBe(208 * 500)
      expect(result.payStructure).toBe('hourly')
    })

    it('should handle overtime for hourly employees', async () => {
      const employee = {
        id: 'EMP002',
        payStructure: 'hourly' as const,
        hourlyRate: 500,
        allowances: [],
        deductions: []
      }

      const attendanceData = {
        workingDays: 26,
        attendedDays: 26,
        totalHours: 218, // 208 regular + 10 overtime
        overtimeHours: 10,
        lateHours: 0,
        earlyLeaveHours: 0
      }

      const result = await engine.calculateHourlyPayroll(employee, attendanceData, '2024-11-01', '2024-11-30')

      const regularPay = 208 * 500
      const overtimePay = (500 * 1.5 * 4) + (500 * 2 * 4) + (500 * 2.5 * 2) // Tiered overtime
      
      expect(result.basePay).toBe(regularPay)
      expect(result.overtimePay).toBeCloseTo(overtimePay, 2)
      expect(result.grossPay).toBeCloseTo(regularPay + overtimePay, 2)
    })
  })

  describe('Daily Wage Calculation', () => {
    it('should calculate daily wages correctly', async () => {
      const employee = {
        id: 'EMP003',
        payStructure: 'daily' as const,
        dailyRate: 2500,
        allowances: [],
        deductions: []
      }

      const attendanceData = {
        workingDays: 26,
        attendedDays: 24,
        overtimeHours: 0,
        lateHours: 0,
        earlyLeaveHours: 0
      }

      const result = await engine.calculateDailyPayroll(employee, attendanceData, '2024-11-01', '2024-11-30')

      expect(result.basePay).toBe(24 * 2500) // 60,000
      expect(result.grossPay).toBe(24 * 2500)
      expect(result.payStructure).toBe('daily')
    })

    it('should handle half-day attendance', async () => {
      const employee = {
        id: 'EMP003',
        payStructure: 'daily' as const,
        dailyRate: 2500,
        allowances: [],
        deductions: []
      }

      const attendanceData = {
        workingDays: 26,
        attendedDays: 24,
        halfDays: 2,
        overtimeHours: 0,
        lateHours: 0,
        earlyLeaveHours: 0
      }

      const result = await engine.calculateDailyPayroll(employee, attendanceData, '2024-11-01', '2024-11-30')

      const expectedPay = (22 * 2500) + (2 * 2500 * 0.5) // 22 full days + 2 half days
      expect(result.basePay).toBe(expectedPay)
    })
  })

  describe('Project-based Calculation', () => {
    it('should calculate project-based payment correctly', async () => {
      const employee = {
        id: 'EMP004',
        payStructure: 'project' as const,
        projectRate: 150000,
        allowances: [],
        deductions: []
      }

      const projectData = {
        completionPercentage: 75,
        milestonePayments: [50000, 50000, 50000], // 3 milestones
        completedMilestones: 2
      }

      const result = await engine.calculateProjectPayroll(employee, projectData, '2024-11-01', '2024-11-30')

      expect(result.basePay).toBe(100000) // 2 completed milestones
      expect(result.grossPay).toBe(100000)
      expect(result.payStructure).toBe('project')
    })
  })

  describe('Allowances and Deductions', () => {
    it('should apply allowances correctly', async () => {
      const allowances = [
        { id: 'transport', name: 'Transport Allowance', amount: 5000, type: 'fixed' as const },
        { id: 'meal', name: 'Meal Allowance', amount: 3000, type: 'fixed' as const }
      ]

      const employee = {
        id: 'EMP001',
        payStructure: 'monthly' as const,
        baseSalary: 65000,
        allowances,
        deductions: []
      }

      const attendanceData = {
        workingDays: 26,
        attendedDays: 26,
        overtimeHours: 0,
        lateHours: 0,
        earlyLeaveHours: 0
      }

      const result = await engine.calculateMonthlyPayroll(employee, attendanceData, '2024-11-01', '2024-11-30')

      expect(result.allowances).toBe(8000)
      expect(result.grossPay).toBe(65000 + 8000)
    })

    it('should apply percentage-based allowances correctly', async () => {
      const allowances = [
        { id: 'performance', name: 'Performance Bonus', amount: 10, type: 'percentage' as const }
      ]

      const employee = {
        id: 'EMP001',
        payStructure: 'monthly' as const,
        baseSalary: 65000,
        allowances,
        deductions: []
      }

      const attendanceData = {
        workingDays: 26,
        attendedDays: 26,
        overtimeHours: 0,
        lateHours: 0,
        earlyLeaveHours: 0
      }

      const result = await engine.calculateMonthlyPayroll(employee, attendanceData, '2024-11-01', '2024-11-30')

      expect(result.allowances).toBe(6500) // 10% of 65000
      expect(result.grossPay).toBe(65000 + 6500)
    })

    it('should apply deductions correctly', async () => {
      const deductions = [
        { id: 'pf', name: 'Provident Fund', amount: 10, type: 'percentage' as const },
        { id: 'insurance', name: 'Health Insurance', amount: 2000, type: 'fixed' as const }
      ]

      const employee = {
        id: 'EMP001',
        payStructure: 'monthly' as const,
        baseSalary: 65000,
        allowances: [],
        deductions
      }

      const attendanceData = {
        workingDays: 26,
        attendedDays: 26,
        overtimeHours: 0,
        lateHours: 0,
        earlyLeaveHours: 0
      }

      const result = await engine.calculateMonthlyPayroll(employee, attendanceData, '2024-11-01', '2024-11-30')

      expect(result.deductions).toBe(6500 + 2000) // 10% of 65000 + 2000
      expect(result.netPay).toBe(65000 - 8500)
    })
  })

  describe('Nepal Labor Law Compliance', () => {
    it('should enforce maximum working hours per day', async () => {
      const employee = {
        id: 'EMP001',
        payStructure: 'hourly' as const,
        hourlyRate: 500,
        allowances: [],
        deductions: []
      }

      const attendanceData = {
        workingDays: 1,
        attendedDays: 1,
        totalHours: 14, // Exceeds 12-hour daily limit
        overtimeHours: 6,
        lateHours: 0,
        earlyLeaveHours: 0
      }

      const result = await engine.calculateHourlyPayroll(employee, attendanceData, '2024-11-01', '2024-11-01')

      expect(result.complianceViolations).toContain('Daily working hours exceed legal limit')
    })

    it('should enforce minimum wage compliance', async () => {
      const employee = {
        id: 'EMP001',
        payStructure: 'monthly' as const,
        baseSalary: 15000, // Below minimum wage of NPR 17,300
        allowances: [],
        deductions: []
      }

      const attendanceData = {
        workingDays: 26,
        attendedDays: 26,
        overtimeHours: 0,
        lateHours: 0,
        earlyLeaveHours: 0
      }

      const result = await engine.calculateMonthlyPayroll(employee, attendanceData, '2024-11-01', '2024-11-30')

      expect(result.complianceViolations).toContain('Salary below minimum wage')
    })

    it('should enforce overtime limits', async () => {
      const employee = {
        id: 'EMP001',
        payStructure: 'hourly' as const,
        hourlyRate: 500,
        allowances: [],
        deductions: []
      }

      const attendanceData = {
        workingDays: 1,
        attendedDays: 1,
        totalHours: 13,
        overtimeHours: 5, // Exceeds 4-hour daily overtime limit
        lateHours: 0,
        earlyLeaveHours: 0
      }

      const result = await engine.calculateHourlyPayroll(employee, attendanceData, '2024-11-01', '2024-11-01')

      expect(result.complianceViolations).toContain('Daily overtime hours exceed legal limit')
    })
  })

  describe('Nepal Calendar Integration', () => {
    it('should handle Bikram Sambat dates correctly', async () => {
      const bsDate = { year: 2081, month: 8, day: 15 } // Mangsir 15, 2081
      const adDate = NepaliCalendar.bsToAD(bsDate)

      expect(adDate).toBeInstanceOf(Date)
      expect(adDate.getFullYear()).toBe(2024)
    })

    it('should identify Nepal holidays correctly', async () => {
      const dashainDate = '2024-10-12' // Dashain 2024
      const isHoliday = await nepalConfig.isHoliday(dashainDate)
      
      expect(isHoliday).toBe(true)
    })

    it('should calculate working days excluding Nepal holidays', async () => {
      const workingDays = nepalConfig.getWorkingDaysInPeriod('2024-10-01', '2024-10-31')
      
      expect(workingDays).toBeLessThan(31) // Should exclude Saturdays and holidays
      expect(workingDays).toBeGreaterThan(20) // Should have reasonable working days
    })
  })

  describe('Currency Formatting', () => {
    it('should format NPR currency with lakhs notation', () => {
      const amount = 1500000
      const formatted = engine.formatCurrency(amount)
      
      expect(formatted).toContain('15,00,000') // Indian numbering system
      expect(formatted).toContain('रू') // NPR symbol
    })

    it('should convert amounts to words in Nepali context', () => {
      const amount = 125000
      const inWords = engine.convertToWords(amount)
      
      expect(inWords).toContain('lakh')
      expect(inWords).toContain('twenty')
      expect(inWords).toContain('five')
      expect(inWords).toContain('thousand')
    })
  })

  describe('Error Handling', () => {
    it('should handle missing employee data gracefully', async () => {
      const invalidEmployee = null

      await expect(
        engine.calculateMonthlyPayroll(invalidEmployee as any, {} as any, '2024-11-01', '2024-11-30')
      ).rejects.toThrow('Invalid employee data')
    })

    it('should handle invalid date ranges', async () => {
      const employee = {
        id: 'EMP001',
        payStructure: 'monthly' as const,
        baseSalary: 65000,
        allowances: [],
        deductions: []
      }

      const attendanceData = {
        workingDays: 26,
        attendedDays: 26,
        overtimeHours: 0,
        lateHours: 0,
        earlyLeaveHours: 0
      }

      await expect(
        engine.calculateMonthlyPayroll(employee, attendanceData, '2024-11-30', '2024-11-01') // Invalid range
      ).rejects.toThrow('Invalid date range')
    })

    it('should handle negative attendance values', async () => {
      const employee = {
        id: 'EMP001',
        payStructure: 'monthly' as const,
        baseSalary: 65000,
        allowances: [],
        deductions: []
      }

      const attendanceData = {
        workingDays: 26,
        attendedDays: -5, // Invalid negative value
        overtimeHours: 0,
        lateHours: 0,
        earlyLeaveHours: 0
      }

      await expect(
        engine.calculateMonthlyPayroll(employee, attendanceData, '2024-11-01', '2024-11-30')
      ).rejects.toThrow('Invalid attendance data')
    })
  })

  describe('Performance Tests', () => {
    it('should calculate payroll for large number of employees efficiently', async () => {
      const startTime = Date.now()
      
      const employees = Array.from({ length: 1000 }, (_, i) => ({
        id: `EMP${i.toString().padStart(3, '0')}`,
        payStructure: 'monthly' as const,
        baseSalary: 65000 + (i * 1000),
        allowances: [],
        deductions: []
      }))

      const attendanceData = {
        workingDays: 26,
        attendedDays: 26,
        overtimeHours: 0,
        lateHours: 0,
        earlyLeaveHours: 0
      }

      const promises = employees.map(employee => 
        engine.calculateMonthlyPayroll(employee, attendanceData, '2024-11-01', '2024-11-30')
      )

      const results = await Promise.all(promises)
      const endTime = Date.now()
      const duration = endTime - startTime

      expect(results).toHaveLength(1000)
      expect(duration).toBeLessThan(5000) // Should complete within 5 seconds
      expect(results.every(r => r.netPay > 0)).toBe(true)
    })
  })
})
