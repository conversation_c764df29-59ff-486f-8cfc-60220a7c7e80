// Nepali Calendar (<PERSON><PERSON><PERSON>) utilities for payroll system
// Phase 1: Nepal Configuration Setup - Calendar Component

export interface BSDate {
  year: number;
  month: number;
  day: number;
}

export interface ADDate {
  year: number;
  month: number;
  day: number;
}

export interface CalendarConversion {
  bsDate: BSDate;
  adDate: ADDate;
  bsString: string; // Format: YYYY-MM-DD
  adString: string; // Format: YYYY-MM-DD
  dayOfWeek: number; // 0=Sunday, 6=Saturday
  isHoliday: boolean;
  isWorkingDay: boolean;
}

// Nepali calendar data - days in each month for different years
// This is a simplified version - in production, you'd use a comprehensive library
const NEPALI_CALENDAR_DATA: { [year: number]: number[] } = {
  2080: [31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30], // 2023-24
  2081: [31, 31, 32, 31, 31, 31, 30, 29, 30, 29, 30, 30], // 2024-25
  2082: [31, 32, 31, 32, 31, 31, 30, 29, 30, 29, 30, 30], // 2025-26
  2083: [31, 32, 31, 32, 31, 31, 30, 29, 30, 29, 30, 30], // 2026-27
  2084: [31, 32, 31, 32, 31, 31, 30, 29, 30, 29, 30, 30], // 2027-28
};

// Reference point for conversion (known BS to AD mapping)
const REFERENCE_BS_DATE = { year: 2081, month: 1, day: 1 }; // BS 2081-01-01
const REFERENCE_AD_DATE = { year: 2024, month: 4, day: 14 }; // AD 2024-04-14

export class NepaliCalendar {
  
  /**
   * Convert Gregorian (AD) date to Bikram Sambat (BS) date
   */
  static adToBS(adDate: string | Date): BSDate {
    const ad = typeof adDate === 'string' ? new Date(adDate) : adDate;
    
    // Calculate days difference from reference point
    const referenceAD = new Date(REFERENCE_AD_DATE.year, REFERENCE_AD_DATE.month - 1, REFERENCE_AD_DATE.day);
    const daysDiff = Math.floor((ad.getTime() - referenceAD.getTime()) / (1000 * 60 * 60 * 24));
    
    // Start from reference BS date and add/subtract days
    let bsYear = REFERENCE_BS_DATE.year;
    let bsMonth = REFERENCE_BS_DATE.month;
    let bsDay = REFERENCE_BS_DATE.day + daysDiff;
    
    // Adjust for month and year boundaries
    while (bsDay > this.getDaysInBSMonth(bsYear, bsMonth)) {
      bsDay -= this.getDaysInBSMonth(bsYear, bsMonth);
      bsMonth++;
      if (bsMonth > 12) {
        bsMonth = 1;
        bsYear++;
      }
    }
    
    while (bsDay < 1) {
      bsMonth--;
      if (bsMonth < 1) {
        bsMonth = 12;
        bsYear--;
      }
      bsDay += this.getDaysInBSMonth(bsYear, bsMonth);
    }
    
    return { year: bsYear, month: bsMonth, day: bsDay };
  }
  
  /**
   * Convert Bikram Sambat (BS) date to Gregorian (AD) date
   */
  static bsToAD(bsDate: BSDate | string): Date {
    let bs: BSDate;
    
    if (typeof bsDate === 'string') {
      const parts = bsDate.split('-').map(Number);
      bs = { year: parts[0], month: parts[1], day: parts[2] };
    } else {
      bs = bsDate;
    }
    
    // Calculate days difference from reference BS date
    let daysDiff = 0;
    
    // Add days for complete years
    for (let year = REFERENCE_BS_DATE.year; year < bs.year; year++) {
      daysDiff += this.getDaysInBSYear(year);
    }
    
    // Add days for complete months in the target year
    for (let month = REFERENCE_BS_DATE.month; month < bs.month; month++) {
      daysDiff += this.getDaysInBSMonth(bs.year, month);
    }
    
    // Add remaining days
    daysDiff += bs.day - REFERENCE_BS_DATE.day;
    
    // Calculate AD date
    const referenceAD = new Date(REFERENCE_AD_DATE.year, REFERENCE_AD_DATE.month - 1, REFERENCE_AD_DATE.day);
    const resultAD = new Date(referenceAD.getTime() + daysDiff * 24 * 60 * 60 * 1000);
    
    return resultAD;
  }
  
  /**
   * Get number of days in a BS month
   */
  static getDaysInBSMonth(year: number, month: number): number {
    const yearData = NEPALI_CALENDAR_DATA[year];
    if (!yearData) {
      // Default fallback - this should be replaced with comprehensive data
      return month === 12 ? 30 : 31;
    }
    return yearData[month - 1] || 30;
  }
  
  /**
   * Get number of days in a BS year
   */
  static getDaysInBSYear(year: number): number {
    const yearData = NEPALI_CALENDAR_DATA[year];
    if (!yearData) {
      return 365; // Default fallback
    }
    return yearData.reduce((sum, days) => sum + days, 0);
  }
  
  /**
   * Format BS date as string
   */
  static formatBSDate(bsDate: BSDate, format: 'YYYY-MM-DD' | 'DD/MM/YYYY' | 'nepali' = 'YYYY-MM-DD'): string {
    const { year, month, day } = bsDate;
    
    switch (format) {
      case 'YYYY-MM-DD':
        return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
      case 'DD/MM/YYYY':
        return `${day.toString().padStart(2, '0')}/${month.toString().padStart(2, '0')}/${year}`;
      case 'nepali':
        // This would include Nepali numerals in a real implementation
        return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
      default:
        return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    }
  }
  
  /**
   * Parse BS date string
   */
  static parseBSDate(dateString: string): BSDate {
    const parts = dateString.split(/[-\/]/).map(Number);
    if (parts.length !== 3) {
      throw new Error('Invalid BS date format. Expected YYYY-MM-DD or DD/MM/YYYY');
    }
    
    // Assume YYYY-MM-DD format if first part is > 31
    if (parts[0] > 31) {
      return { year: parts[0], month: parts[1], day: parts[2] };
    } else {
      // Assume DD/MM/YYYY format
      return { year: parts[2], month: parts[1], day: parts[0] };
    }
  }
  
  /**
   * Get current BS date
   */
  static getCurrentBSDate(): BSDate {
    return this.adToBS(new Date());
  }
  
  /**
   * Get current AD date
   */
  static getCurrentADDate(): Date {
    return new Date();
  }
  
  /**
   * Check if BS date is valid
   */
  static isValidBSDate(bsDate: BSDate): boolean {
    const { year, month, day } = bsDate;
    
    if (month < 1 || month > 12) return false;
    if (day < 1) return false;
    
    const daysInMonth = this.getDaysInBSMonth(year, month);
    return day <= daysInMonth;
  }
  
  /**
   * Get BS fiscal year from BS date
   */
  static getBSFiscalYear(bsDate: BSDate): string {
    const { year, month } = bsDate;
    
    // Fiscal year starts from Shrawan (month 4)
    if (month >= 4) {
      return `${year}-${(year + 1).toString().slice(-2)}`;
    } else {
      return `${year - 1}-${year.toString().slice(-2)}`;
    }
  }
  
  /**
   * Get fiscal year start and end dates in BS
   */
  static getFiscalYearDates(fiscalYear: string): { start: BSDate; end: BSDate } {
    const [startYear] = fiscalYear.split('-').map(Number);
    
    return {
      start: { year: startYear, month: 4, day: 1 }, // 1st Shrawan
      end: { year: startYear + 1, month: 3, day: this.getDaysInBSMonth(startYear + 1, 3) } // Last day of Ashadh
    };
  }
  
  /**
   * Get month name in Nepali
   */
  static getBSMonthName(month: number, language: 'en' | 'ne' = 'en'): string {
    const monthNames = {
      en: ['Baishakh', 'Jestha', 'Ashadh', 'Shrawan', 'Bhadra', 'Ashwin',
           'Kartik', 'Mangsir', 'Poush', 'Magh', 'Falgun', 'Chaitra'],
      ne: ['बैशाख', 'जेठ', 'आषाढ', 'श्रावण', 'भाद्र', 'आश्विन',
           'कार्तिक', 'मंसिर', 'पौष', 'माघ', 'फाल्गुन', 'चैत्र']
    };
    
    return monthNames[language][month - 1] || '';
  }
  
  /**
   * Convert date with full context
   */
  static convertWithContext(date: string | Date): CalendarConversion {
    const adDate = typeof date === 'string' ? new Date(date) : date;
    const bsDate = this.adToBS(adDate);
    
    return {
      bsDate,
      adDate: {
        year: adDate.getFullYear(),
        month: adDate.getMonth() + 1,
        day: adDate.getDate()
      },
      bsString: this.formatBSDate(bsDate),
      adString: adDate.toISOString().split('T')[0],
      dayOfWeek: adDate.getDay(),
      isHoliday: false, // This would be determined by holiday configuration
      isWorkingDay: true // This would be determined by working day rules
    };
  }
  
  /**
   * Get date range in BS format
   */
  static getBSDateRange(startAD: string, endAD: string): { start: BSDate; end: BSDate } {
    return {
      start: this.adToBS(startAD),
      end: this.adToBS(endAD)
    };
  }
  
  /**
   * Calculate days between two BS dates
   */
  static daysBetweenBS(startBS: BSDate, endBS: BSDate): number {
    const startAD = this.bsToAD(startBS);
    const endAD = this.bsToAD(endBS);
    
    return Math.floor((endAD.getTime() - startAD.getTime()) / (1000 * 60 * 60 * 24));
  }
  
  /**
   * Add days to BS date
   */
  static addDaysToBS(bsDate: BSDate, days: number): BSDate {
    const adDate = this.bsToAD(bsDate);
    const newADDate = new Date(adDate.getTime() + days * 24 * 60 * 60 * 1000);
    return this.adToBS(newADDate);
  }
  
  /**
   * Get working days between two dates
   */
  static getWorkingDaysBetween(startDate: string, endDate: string, weeklyOffs: number[] = [6]): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    let workingDays = 0;
    
    const currentDate = new Date(start);
    while (currentDate <= end) {
      const dayOfWeek = currentDate.getDay();
      if (!weeklyOffs.includes(dayOfWeek)) {
        workingDays++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return workingDays;
  }
}

// Utility functions for common operations
export const nepaliCalendarUtils = {
  
  // Get current dates in both calendars
  getCurrentDates(): { bs: BSDate; ad: Date; bsString: string; adString: string } {
    const ad = new Date();
    const bs = NepaliCalendar.adToBS(ad);
    
    return {
      bs,
      ad,
      bsString: NepaliCalendar.formatBSDate(bs),
      adString: ad.toISOString().split('T')[0]
    };
  },
  
  // Format date for display (both calendars)
  formatDateDisplay(date: string | Date, showBoth: boolean = true): string {
    const conversion = NepaliCalendar.convertWithContext(date);
    
    if (showBoth) {
      return `${conversion.adString} (${conversion.bsString} BS)`;
    }
    return conversion.adString;
  },
  
  // Get fiscal year info
  getFiscalYearInfo(date?: string): {
    fiscalYear: string;
    startDate: string;
    endDate: string;
    bsStartDate: string;
    bsEndDate: string;
  } {
    const targetDate = date ? new Date(date) : new Date();
    const bsDate = NepaliCalendar.adToBS(targetDate);
    const fiscalYear = NepaliCalendar.getBSFiscalYear(bsDate);
    const fiscalDates = NepaliCalendar.getFiscalYearDates(fiscalYear);
    
    return {
      fiscalYear,
      startDate: NepaliCalendar.bsToAD(fiscalDates.start).toISOString().split('T')[0],
      endDate: NepaliCalendar.bsToAD(fiscalDates.end).toISOString().split('T')[0],
      bsStartDate: NepaliCalendar.formatBSDate(fiscalDates.start),
      bsEndDate: NepaliCalendar.formatBSDate(fiscalDates.end)
    };
  }
};

export default NepaliCalendar;
